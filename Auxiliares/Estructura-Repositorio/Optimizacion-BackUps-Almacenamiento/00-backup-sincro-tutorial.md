# Tutorial: Sistema de Sincronización Automática de Nombres de Archivos

## 📋 Descripción General

Este sistema permite mantener automáticamente sincronizados los nombres de archivos idénticos distribuidos en diferentes carpetas del proyecto. Cuando un archivo cambia de nombre, todos sus "hermanos" (archivos con el mismo nombre en otras ubicaciones) se renombran automáticamente.

## 🎯 Casos de Uso

### Ejemplo Práctico
```
Proyecto/
├── Lab-Lubuntu/01-ejercicio-matematicas.Rmd
├── Lab-Manjaro/01-ejercicio-matematicas.Rmd
└── Backup/01-ejercicio-matematicas.Rmd
```

Si renombras `Lab-Lubuntu/01-ejercicio-matematicas.Rmd` a `Lab-Lubuntu/02-ejercicio-avanzado.Rmd`, automáticamente:
- `Lab-Manjaro/01-ejercicio-matematicas.Rmd` → `Lab-Manjaro/02-ejercicio-avanzado.Rmd`
- `Backup/01-ejercicio-matematicas.Rmd` → `Backup/02-ejercicio-avanzado.Rmd`

## 🔧 Componentes del Sistema

### 1. **01-sync-detector.sh** - Detector de Archivos Duplicados
- Escanea el proyecto buscando archivos con nombres idénticos
- Crea grupos de sincronización
- Genera base de datos de archivos vinculados

### 2. **02-sync-monitor.sh** - Monitor de Cambios
- Usa `inotify` para detectar cambios de nombres en tiempo real
- Ejecuta sincronización automática cuando detecta cambios
- Mantiene logs detallados de todas las operaciones

### 3. **03-sync-engine.sh** - Motor de Sincronización
- Ejecuta el renombrado sincronizado
- Maneja conflictos y errores
- Implementa sistema de locks para evitar bucles infinitos

### 4. **04-sync-config.conf** - Configuración
- Rutas a incluir/excluir del monitoreo
- Patrones de archivos a sincronizar
- Configuraciones de logging

### 5. **05-sync-database.txt** - Base de Datos
- Registro de grupos de archivos sincronizados
- Timestamps de última sincronización
- Estados de archivos

## 🚀 Instalación y Configuración

### Paso 1: Preparar el Sistema
```bash
# Instalar dependencias (si no están instaladas)
sudo apt-get install inotify-tools findutils

# Dar permisos de ejecución a todos los scripts
chmod +x 01-sync-detector.sh
chmod +x 02-sync-monitor.sh
chmod +x 03-sync-engine.sh
```

### Paso 2: Configuración Inicial
```bash
# Ejecutar detección inicial de archivos duplicados
./01-sync-detector.sh

# Revisar la base de datos generada
cat 05-sync-database.txt
```

### Paso 3: Iniciar Monitoreo
```bash
# Iniciar el monitor en background
./02-sync-monitor.sh &

# O usar systemd para servicio permanente (opcional)
sudo cp sync-monitor.service /etc/systemd/system/
sudo systemctl enable sync-monitor.service
sudo systemctl start sync-monitor.service
```

## 📊 Funcionamiento Interno

### Flujo de Trabajo
1. **Detección**: El sistema escanea periódicamente buscando archivos con nombres idénticos
2. **Agrupación**: Crea grupos de archivos que deben mantenerse sincronizados
3. **Monitoreo**: `inotify` detecta cuando un archivo cambia de nombre
4. **Sincronización**: Automáticamente renombra todos los archivos del mismo grupo
5. **Logging**: Registra todas las operaciones para auditoría

### Prevención de Conflictos
- **Sistema de Locks**: Evita renombrados simultáneos
- **Validación**: Verifica que el nuevo nombre no cause conflictos
- **Rollback**: Capacidad de deshacer cambios problemáticos
- **Timeouts**: Evita operaciones que se cuelguen

## 🛡️ Características de Seguridad

### Protecciones Implementadas
- ✅ **Anti-bucles**: Previene renombrados infinitos
- ✅ **Validación de rutas**: Solo opera dentro del proyecto definido
- ✅ **Backup automático**: Crea respaldos antes de cambios críticos
- ✅ **Logs detallados**: Auditoría completa de todas las operaciones
- ✅ **Modo dry-run**: Permite probar cambios sin ejecutarlos

### Exclusiones Automáticas
- Archivos del sistema (`.git/`, `node_modules/`, etc.)
- Archivos temporales (`.tmp`, `.swp`, etc.)
- Archivos de configuración críticos

## 📈 Monitoreo y Mantenimiento

### Comandos Útiles
```bash
# Ver estado actual del sistema
./01-sync-detector.sh --status

# Forzar re-escaneo completo
./01-sync-detector.sh --rescan

# Ver logs recientes
tail -f sync-monitor.log

# Detener monitoreo
pkill -f sync-monitor.sh
```

### Archivos de Log
- `sync-monitor.log`: Log principal del monitor
- `sync-operations.log`: Registro de todas las operaciones de sincronización
- `sync-errors.log`: Errores y advertencias

## 🔍 Resolución de Problemas

### Problemas Comunes

**1. El monitor no detecta cambios**
```bash
# Verificar que inotify esté funcionando
inotifywait -m /ruta/del/proyecto

# Revisar límites del sistema
cat /proc/sys/fs/inotify/max_user_watches
```

**2. Archivos no se sincronizan**
```bash
# Verificar que estén en la base de datos
grep "nombre-archivo" 05-sync-database.txt

# Re-ejecutar detección
./01-sync-detector.sh --force
```

**3. Conflictos de nombres**
```bash
# Ver conflictos pendientes
./03-sync-engine.sh --check-conflicts

# Resolver manualmente
./03-sync-engine.sh --resolve-conflict "grupo-id"
```

## 📋 Próximos Pasos

1. **Ejecutar**: `./01-sync-detector.sh` para crear la base de datos inicial
2. **Configurar**: Editar `04-sync-config.conf` según tus necesidades
3. **Probar**: Usar modo dry-run para verificar funcionamiento
4. **Activar**: Iniciar el monitor para sincronización automática
5. **Monitorear**: Revisar logs regularmente

## 🎯 Beneficios del Sistema

- ✅ **Consistencia automática** de nombres de archivos
- ✅ **Reducción de errores** manuales
- ✅ **Ahorro de tiempo** en mantenimiento
- ✅ **Auditoría completa** de cambios
- ✅ **Escalabilidad** para proyectos grandes
- ✅ **Flexibilidad** en configuración

---

**Nota**: Este sistema está diseñado específicamente para el proyecto RepositorioMatematicasICFES_R_Exams y puede adaptarse a otros proyectos similares.
