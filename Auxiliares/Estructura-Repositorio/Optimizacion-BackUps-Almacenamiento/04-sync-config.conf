# ============================================================================
# CONFIGURACIÓN DEL SISTEMA DE SINCRONIZACIÓN AUTOMÁTICA
# ============================================================================
# Descripción: Archivo de configuración para el sistema de sincronización
#              automática de nombres de archivos duplicados
# Autor: Sistema de Sincronización Automática
# Fecha: $(date +%Y-%m-%d)
# ============================================================================

# ============================================================================
# CONFIGURACIÓN GENERAL
# ============================================================================

# Directorio raíz del proyecto
PROJECT_ROOT="/home/<USER>/Insync/<EMAIL>/Google Drive/RepositorioMatematicasICFES_R_Exams"

# Modo verbose para logging detallado
VERBOSE=true

# Habilitar backups automáticos antes de sincronización
BACKUP_BEFORE_SYNC=true

# Verificar integridad después de sincronización
VERIFY_AFTER_SYNC=true

# ============================================================================
# CONFIGURACIÓN DE ARCHIVOS A INCLUIR
# ============================================================================

# Patrones de archivos a incluir en la sincronización
# Usar wildcards estándar de shell
INCLUDE_PATTERNS=(
    "*.Rmd"           # Archivos R Markdown
    "*.R"             # Scripts de R
    "*.md"            # Archivos Markdown
    "*.txt"           # Archivos de texto
    "*.sh"            # Scripts de shell
    "*.py"            # Scripts de Python
    "*.html"          # Archivos HTML
    "*.css"           # Hojas de estilo
    "*.js"            # Scripts JavaScript
    "*.json"          # Archivos JSON
    "*.xml"           # Archivos XML
    "*.csv"           # Archivos CSV
    "*.pdf"           # Documentos PDF (solo si son pequeños)
    "*.png"           # Imágenes PNG
    "*.jpg"           # Imágenes JPEG
    "*.jpeg"          # Imágenes JPEG
    "*.svg"           # Imágenes SVG
)

# ============================================================================
# CONFIGURACIÓN DE EXCLUSIONES
# ============================================================================

# Directorios a excluir del monitoreo
EXCLUDE_DIRS=(
    ".git"                    # Control de versiones Git
    ".svn"                    # Control de versiones SVN
    "node_modules"            # Dependencias de Node.js
    ".Rproj.user"            # Archivos de usuario de RStudio
    "rsconnect"              # Configuración de RStudio Connect
    ".vscode"                # Configuración de Visual Studio Code
    ".idea"                  # Configuración de IntelliJ IDEA
    "__pycache__"            # Cache de Python
    ".pytest_cache"          # Cache de pytest
    "venv"                   # Entornos virtuales de Python
    "env"                    # Entornos virtuales de Python
    ".env"                   # Variables de entorno
    "dist"                   # Directorios de distribución
    "build"                  # Directorios de construcción
    "target"                 # Directorios de construcción (Java/Scala)
    ".tmp"                   # Archivos temporales
    "temp"                   # Archivos temporales
    "cache"                  # Directorios de cache
    "logs"                   # Directorios de logs (excepto los nuestros)
    ".DS_Store"              # Archivos del sistema macOS
    "Thumbs.db"              # Archivos del sistema Windows
)

# Patrones de archivos a excluir
EXCLUDE_PATTERNS=(
    ".*"                     # Archivos ocultos (que empiecen con punto)
    "*~"                     # Archivos de backup de editores
    "*.tmp"                  # Archivos temporales
    "*.temp"                 # Archivos temporales
    "*.swp"                  # Archivos de swap de vim
    "*.swo"                  # Archivos de swap de vim
    "*.bak"                  # Archivos de backup
    "*.old"                  # Archivos antiguos
    "*.orig"                 # Archivos originales de merge
    "*.rej"                  # Archivos de rechazo de patch
    "*.log"                  # Archivos de log (excepto los nuestros)
    "*.pid"                  # Archivos de PID
    "*.lock"                 # Archivos de lock
    "*#*"                    # Archivos de Emacs
    ".#*"                    # Archivos de Emacs
)

# ============================================================================
# CONFIGURACIÓN DE TAMAÑOS DE ARCHIVO
# ============================================================================

# Tamaño mínimo de archivo en bytes (1 byte)
MIN_FILE_SIZE=1

# Tamaño máximo de archivo en bytes (100 MB)
# Archivos muy grandes pueden causar problemas de rendimiento
MAX_FILE_SIZE=104857600

# ============================================================================
# CONFIGURACIÓN DEL MONITOR
# ============================================================================

# Intervalo de debounce en segundos para evitar eventos duplicados
DEBOUNCE_INTERVAL=2

# Monitorear subdirectorios recursivamente
MONITOR_SUBDIRS=true

# Timeout para adquisición de locks en segundos
LOCK_TIMEOUT=30

# Número máximo de reintentos para operaciones fallidas
MAX_RETRIES=3

# Intervalo entre reintentos en segundos
RETRY_INTERVAL=1

# ============================================================================
# CONFIGURACIÓN DE LOGGING
# ============================================================================

# Nivel de logging: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL="INFO"

# Rotar logs cuando excedan este tamaño (en bytes)
MAX_LOG_SIZE=10485760  # 10 MB

# Número máximo de archivos de log a mantener
MAX_LOG_FILES=5

# Formato de timestamp para logs
LOG_TIMESTAMP_FORMAT="%Y-%m-%d %H:%M:%S"

# ============================================================================
# CONFIGURACIÓN DE BACKUPS
# ============================================================================

# Directorio para backups automáticos (relativo al directorio del script)
BACKUP_DIR="backups"

# Número máximo de backups a mantener por archivo
MAX_BACKUPS_PER_FILE=10

# Comprimir backups antiguos (requiere gzip)
COMPRESS_OLD_BACKUPS=true

# Edad en días después de la cual comprimir backups
COMPRESS_AFTER_DAYS=7

# Eliminar backups después de este número de días
DELETE_BACKUPS_AFTER_DAYS=30

# ============================================================================
# CONFIGURACIÓN DE SEGURIDAD
# ============================================================================

# Validar que las rutas estén dentro del proyecto
VALIDATE_PATHS=true

# Crear checksums de archivos para verificación de integridad
CREATE_CHECKSUMS=false

# Requerir confirmación para operaciones destructivas
REQUIRE_CONFIRMATION=false

# Modo de solo lectura (no realizar cambios reales)
READ_ONLY_MODE=false

# ============================================================================
# CONFIGURACIÓN DE RENDIMIENTO
# ============================================================================

# Número máximo de archivos a procesar en paralelo
MAX_PARALLEL_OPERATIONS=4

# Usar cache para acelerar búsquedas repetidas
USE_CACHE=true

# Tiempo de vida del cache en segundos
CACHE_TTL=300  # 5 minutos

# ============================================================================
# CONFIGURACIÓN DE NOTIFICACIONES
# ============================================================================

# Enviar notificaciones del sistema (requiere notify-send)
ENABLE_NOTIFICATIONS=false

# Comando para enviar notificaciones por email (opcional)
EMAIL_COMMAND=""

# Dirección de email para notificaciones críticas
ADMIN_EMAIL=""

# ============================================================================
# CONFIGURACIÓN AVANZADA
# ============================================================================

# Usar inotify para monitoreo en tiempo real (Linux)
USE_INOTIFY=true

# Usar fswatch como alternativa a inotify (macOS/BSD)
USE_FSWATCH=false

# Intervalo de polling en segundos si no hay monitoreo en tiempo real
POLLING_INTERVAL=10

# Detectar movimientos de archivos entre directorios
DETECT_MOVES=true

# Detectar cambios de permisos de archivos
DETECT_PERMISSION_CHANGES=false

# ============================================================================
# CONFIGURACIÓN DE DESARROLLO/DEBUG
# ============================================================================

# Modo de desarrollo con logging extra
DEVELOPMENT_MODE=false

# Crear archivos de trace para debugging
CREATE_TRACE_FILES=false

# Directorio para archivos de trace
TRACE_DIR="traces"

# Simular errores para testing (porcentaje de fallos)
SIMULATE_ERRORS=0

# ============================================================================
# CONFIGURACIÓN DE INTEGRACIÓN
# ============================================================================

# Ejecutar hooks antes de sincronización
PRE_SYNC_HOOK=""

# Ejecutar hooks después de sincronización
POST_SYNC_HOOK=""

# Integración con Git (commit automático después de sincronización)
AUTO_GIT_COMMIT=false

# Mensaje de commit automático
AUTO_COMMIT_MESSAGE="Sincronización automática de archivos"

# ============================================================================
# CONFIGURACIÓN DE RECUPERACIÓN
# ============================================================================

# Crear punto de restauración antes de operaciones críticas
CREATE_RESTORE_POINTS=true

# Directorio para puntos de restauración
RESTORE_POINTS_DIR="restore_points"

# Número máximo de puntos de restauración
MAX_RESTORE_POINTS=20

# ============================================================================
# FIN DE CONFIGURACIÓN
# ============================================================================

# Nota: Después de modificar este archivo, reiniciar el monitor para
#       que los cambios tomen efecto:
#       ./02-sync-monitor.sh restart
